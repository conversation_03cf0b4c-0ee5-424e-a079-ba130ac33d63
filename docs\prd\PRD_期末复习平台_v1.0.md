# 产品需求文档 (PRD) - 期末复习平台

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **产品名称** | 期末复习平台 |
| **版本** | v1.0.0 |
| **文档版本** | v1.0.0 |
| **创建日期** | 2025-01-08 |
| **最后更新** | 2025-01-08 |
| **负责人** | Emma (产品经理) |
| **状态** | 待评审 |
| **项目代号** | TermReview |

### 版本历史
| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| v1.0.0 | 2025-01-08 | 基于草案创建完整PRD | Emma |

## 2. 背景与问题陈述

### 2.1 项目背景
在学习过程中，学生和教师经常需要整理、分享和查阅大量的学习笔记。传统的笔记管理方式存在以下痛点：
- 笔记分散存储，难以统一管理和查找
- 缺乏有效的分类和组织方式
- 难以实现跨设备、跨平台的便捷访问
- 图文混排的笔记在不同平台显示效果不一致
- 缺乏简单易用的分享机制

### 2.2 核心问题
**如何为学习者提供一个简单、高效、可持久保存的在线笔记管理和分享平台？**

### 2.3 解决方案概述
构建一个基于Markdown的在线笔记平台，支持：
- 管理员便捷上传和管理笔记内容
- 访客免登录浏览所有公开内容
- 完整保留文件夹层级结构
- 图文混排内容的完美展示
- 响应式设计，支持多设备访问

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **O1 - 提升笔记管理效率**: 为管理员提供批量上传、分类管理笔记的能力
2. **O2 - 优化内容访问体验**: 为访客提供便捷、美观的笔记浏览体验
3. **O3 - 确保数据持久性**: 建立可靠的数据存储和备份机制
4. **O4 - 实现跨平台兼容**: 支持桌面、平板、手机等多设备访问

### 3.2 关键结果 (Key Results)
1. **KR1**: 管理员可在5分钟内完成一个学科的完整笔记上传
2. **KR2**: 访客可在3秒内加载并浏览任意笔记内容
3. **KR3**: 系统支持单学科最大500MB内容存储
4. **KR4**: 在主流浏览器和移动设备上兼容性达到100%

### 3.3 反向指标 (Counter Metrics)
- 上传失败率 < 1%
- 图片加载失败率 < 2%
- 系统响应时间 < 3秒
- 移动端布局异常率 < 1%

## 4. 用户画像与用户故事

### 4.1 目标用户

#### 主要用户：管理员
- **角色描述**: 教师、学习小组组长、知识管理者
- **核心需求**: 高效管理和发布学习资料
- **使用场景**: 期末复习前整理课程笔记，课程结束后归档资料
- **技术水平**: 中等，熟悉基本的文件操作

#### 次要用户：访客
- **角色描述**: 学生、学习者、知识获取者
- **核心需求**: 便捷获取和阅读学习资料
- **使用场景**: 复习备考、知识查阅、学习参考
- **技术水平**: 基础，主要使用浏览器访问

### 4.2 用户故事

#### 管理员用户故事
1. **作为管理员，我希望能够创建不同的学科分类，以便更好地组织我的笔记内容**
   - 验收标准：可以创建新学科，学科名称唯一，创建后立即可见

2. **作为管理员，我希望能够批量上传包含图片的Markdown文件夹，以便快速发布完整的课程资料**
   - 验收标准：支持文件夹拖拽上传，保持原有目录结构，图片正确显示

3. **作为管理员，我希望能够更新已有的笔记内容，以便及时修正和补充资料**
   - 验收标准：同名文件夹覆盖更新，有二次确认提示，更新后内容立即生效

4. **作为管理员，我希望能够删除不需要的学科或文件，以便保持内容的整洁**
   - 验收标准：支持删除学科和单个文件，有二次确认，删除后立即生效

#### 访客用户故事
1. **作为访客，我希望能够浏览所有可用的学科，以便找到我需要的学习资料**
   - 验收标准：首页显示所有学科列表，按字母排序，加载速度快

2. **作为访客，我希望能够查看学科内的文件结构，以便快速定位具体内容**
   - 验收标准：文件夹优先显示，内容按字母排序，支持层级导航

3. **作为访客，我希望能够阅读格式良好的Markdown内容，以便获得良好的学习体验**
   - 验收标准：Markdown正确渲染，图片正常显示，代码高亮，响应式布局

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 学科管理模块
**功能描述**: 管理员创建和管理学科分类

**业务流程**:
```
管理员访问管理后台 → 点击"新建学科" → 输入学科名称 → 确认创建 → 学科列表更新
```

**业务规则**:
- 学科名称必须唯一
- 学科名称长度限制：1-50个字符
- 删除学科时需二次确认
- 删除学科会同时删除其下所有内容

**异常处理**:
- 学科名称重复：提示用户修改名称
- 创建失败：显示具体错误信息并允许重试

#### 5.1.2 文件上传模块
**功能描述**: 管理员批量上传Markdown文件和图片

**业务流程**:
```
选择目标学科 → 拖拽/选择文件夹 → 系统检查文件类型 → 预览上传内容 → 确认上传 → 处理文件 → 更新数据库 → 完成反馈
```

**业务规则**:
- 支持文件类型：.md, .jpg, .jpeg, .png, .gif, .svg, .webp
- 单文件大小限制：20MB
- 单次上传总量限制：500MB
- 同名文件夹执行覆盖更新
- 保持原有目录结构

**异常处理**:
- 文件过大：提示压缩或分批上传
- 不支持的文件类型：自动忽略，不报错
- 上传失败：回滚所有操作，保持数据一致性

#### 5.1.3 内容浏览模块
**功能描述**: 访客浏览和阅读笔记内容

**业务流程**:
```
访问首页 → 查看学科列表 → 选择学科 → 浏览文件结构 → 选择文件 → 阅读内容
```

**业务规则**:
- 所有内容对访客开放
- 文件夹优先，按字母排序
- Markdown内容正确渲染
- 图片通过相对路径正确显示
- 响应式布局适配各种设备

**异常处理**:
- 文件不存在：显示404页面
- 图片加载失败：显示占位符和alt文本
- 内容加载慢：显示加载动画

### 5.2 技术实现要点

#### 5.2.1 图片路径处理
- 上传时自动扫描Markdown文件中的相对图片路径
- 将相对路径替换为服务器API路径：`/api/assets/{file_node_id}`
- 确保图片在各种设备上正确显示

#### 5.2.2 原子化操作
- 文件上传采用事务机制
- 失败时完整回滚，避免数据不一致
- 使用临时目录确保操作安全性

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 学科分类管理
- ✅ 批量文件上传
- ✅ Markdown内容渲染
- ✅ 图片显示支持
- ✅ 文件夹结构保持
- ✅ 响应式布局
- ✅ 内容删除功能
- ✅ 访客浏览功能

### 6.2 排除功能 (Out of Scope)
- ❌ 用户注册登录系统
- ❌ 评论和互动功能
- ❌ 内容搜索功能
- ❌ 版本控制功能
- ❌ 协作编辑功能
- ❌ 数据统计分析
- ❌ 移动端原生应用
- ❌ 离线访问支持

## 7. 依赖与风险

### 7.1 内部依赖
- 需要Bob完成系统架构设计
- 需要Alex完成前后端开发
- 需要David进行性能测试和优化

### 7.2 外部依赖
- Docker运行环境
- 服务器存储空间
- 网络带宽资源

### 7.3 潜在风险

| 风险 | 影响程度 | 发生概率 | 缓解措施 |
|------|----------|----------|----------|
| 大文件上传性能问题 | 高 | 中 | 实施分片上传，优化传输机制 |
| SQLite并发访问限制 | 中 | 低 | 监控并发量，必要时升级数据库 |
| 存储空间不足 | 高 | 低 | 实施存储监控和清理策略 |
| 图片加载速度慢 | 中 | 中 | 实施图片压缩和CDN加速 |

## 8. 发布初步计划

### 8.1 发布策略
- **Alpha版本**: 内部测试，验证核心功能
- **Beta版本**: 小范围用户测试，收集反馈
- **正式版本**: 全量发布，持续监控

### 8.2 验收标准
- 所有核心功能正常运行
- 性能指标达到预期
- 兼容性测试通过
- 安全测试通过

### 8.3 上线检查清单
- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] 安全检查通过
- [ ] 文档完善
- [ ] 部署环境就绪
- [ ] 监控系统配置
- [ ] 备份策略实施

## 9. 技术约束与非功能性需求

### 9.1 性能要求
- **响应时间**: 页面加载时间 < 3秒
- **并发用户**: 支持100个并发访客浏览
- **文件上传**: 单次上传完成时间 < 5分钟（500MB）
- **数据库查询**: 查询响应时间 < 1秒

### 9.2 安全要求
- **访问控制**: 管理后台通过特定URL路径保护
- **文件过滤**: 严格限制上传文件类型
- **数据完整性**: 使用事务确保数据一致性
- **错误处理**: 不暴露系统内部信息

### 9.3 可用性要求
- **系统可用性**: 99.5%
- **数据持久性**: 99.9%
- **备份恢复**: 支持完整数据备份和恢复
- **容错能力**: 单点故障不影响整体服务

### 9.4 兼容性要求
- **浏览器支持**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **移动设备**: iOS 14+, Android 10+
- **屏幕分辨率**: 320px - 2560px 自适应

## 10. 接口规范概述

### 10.1 RESTful API设计原则
- 使用标准HTTP方法（GET, POST, DELETE）
- 统一的响应格式
- 合理的HTTP状态码
- 清晰的错误信息

### 10.2 核心API列表

| 接口路径 | 方法 | 功能描述 | 权限要求 |
|----------|------|----------|----------|
| `/api/subjects` | GET | 获取所有学科列表 | 公开 |
| `/api/subjects` | POST | 创建新学科 | 管理员 |
| `/api/subjects/{id}` | DELETE | 删除指定学科 | 管理员 |
| `/api/subjects/{id}/files` | GET | 获取学科文件结构 | 公开 |
| `/api/subjects/{id}/upload` | POST | 上传文件到学科 | 管理员 |
| `/api/files/{fileId}` | GET | 获取Markdown文件内容 | 公开 |
| `/api/file_nodes/{id}` | DELETE | 删除文件或文件夹 | 管理员 |
| `/api/assets/{file_node_id}` | GET | 获取静态资源文件 | 公开 |

### 10.3 数据模型概述

#### 学科模型 (Subject)
```json
{
  "id": "integer",
  "name": "string",
  "created_at": "timestamp"
}
```

#### 文件节点模型 (FileNode)
```json
{
  "id": "integer",
  "subject_id": "integer",
  "parent_id": "integer|null",
  "name": "string",
  "type": "file|folder",
  "relative_path": "string",
  "storage_url": "string|null",
  "created_at": "timestamp"
}
```

## 11. 用户体验设计要求

### 11.1 界面设计原则
- **简洁明了**: 界面简洁，功能清晰
- **一致性**: 保持设计风格统一
- **响应式**: 适配各种设备屏幕
- **可访问性**: 支持键盘导航和屏幕阅读器

### 11.2 交互设计要求
- **操作反馈**: 所有操作都有明确反馈
- **错误提示**: 友好的错误信息和解决建议
- **加载状态**: 长时间操作显示进度指示
- **确认机制**: 危险操作需要二次确认

### 11.3 内容展示要求
- **Markdown渲染**: 支持完整的Markdown语法
- **代码高亮**: 代码块语法高亮显示
- **图片优化**: 自动适配屏幕大小
- **导航便利**: 清晰的面包屑导航

## 12. 测试策略

### 12.1 测试范围
- **功能测试**: 验证所有功能正常工作
- **性能测试**: 验证系统性能指标
- **兼容性测试**: 验证多浏览器和设备兼容性
- **安全测试**: 验证访问控制和数据安全

### 12.2 测试用例概述
- 学科创建、删除功能测试
- 文件上传、更新功能测试
- 内容浏览、显示功能测试
- 异常情况处理测试
- 性能压力测试

## 13. 运维与监控

### 13.1 部署要求
- **容器化部署**: 使用Docker和docker-compose
- **数据持久化**: 使用Docker Volume保证数据安全
- **服务编排**: Nginx + Node.js + PM2架构
- **环境配置**: 支持开发、测试、生产环境

### 13.2 监控指标
- **系统性能**: CPU、内存、磁盘使用率
- **应用性能**: 响应时间、错误率、并发数
- **业务指标**: 上传成功率、访问量、存储使用量

## 14. 项目里程碑

### 14.1 开发阶段
| 阶段 | 时间 | 主要交付物 | 负责人 |
|------|------|------------|--------|
| 需求分析 | Week 1 | PRD文档、任务规划 | Emma |
| 架构设计 | Week 1-2 | 架构文档、技术选型 | Bob |
| 后端开发 | Week 2-3 | API接口、数据库 | Alex |
| 前端开发 | Week 3-4 | 用户界面、交互功能 | Alex |
| 集成测试 | Week 4 | 测试报告、Bug修复 | Alex |
| 部署上线 | Week 5 | 生产环境部署 | Alex |

### 14.2 验收里程碑
- **Alpha版本**: 核心功能完成，内部测试通过
- **Beta版本**: 所有功能完成，用户测试通过
- **正式版本**: 性能优化完成，正式发布

---

**文档结束**

*本PRD文档将作为项目开发的核心指导文件，所有功能实现都应严格按照本文档执行。如有变更需求，请及时更新本文档并通知相关团队成员。*
