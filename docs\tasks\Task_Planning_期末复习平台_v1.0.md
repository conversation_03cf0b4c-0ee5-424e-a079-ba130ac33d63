# 任务规划文档 - 期末复习平台

## 文档信息

| 项目 | 内容 |
|------|------|
| **项目名称** | 期末复习平台 |
| **版本** | v1.0.0 |
| **文档版本** | v1.0.0 |
| **创建日期** | 2025-01-08 |
| **最后更新** | 2025-01-08 |
| **负责人** | Emma (产品经理) |
| **状态** | 已完成 |

## 项目概述

基于完整PRD文档，构建一个支持管理员上传Markdown笔记、访客免登录浏览的在线学习平台。采用Vue3+TypeScript前端、Node.js+Koa后端、SQLite数据库、Docker部署的技术架构。

## 任务拆分方法论

采用**垂直切片（Vertical Slicing）**方法论，每个切片都是端到端的完整功能，能够为用户提供独立的价值。避免按技术层次（前端、后端、数据库）水平拆分，确保每个任务完成后都有可交付的功能。

## 任务列表

### 任务1：项目基础设施搭建
- **任务ID**: `e37c3490-2167-4719-8eda-e29d06b9a55a`
- **优先级**: P0（最高优先级）
- **预估工期**: 2-3天
- **负责人**: Alex (工程师)
- **依赖任务**: 无

**任务描述**：
搭建前后端项目结构，配置开发环境，建立数据库连接和基础API框架。包括Vue3+Vite前端项目、Node.js+Koa后端项目、SQLite数据库初始化、开发工具配置等。

**实现指南**：
1. 使用Vite创建Vue3+TypeScript项目，配置Ant Design Vue和UnoCSS
2. 创建Node.js项目，安装Koa、better-sqlite3等依赖
3. 设计并创建subjects和file_nodes数据表
4. 实现基础的API路由框架和错误处理中间件
5. 配置ESLint、Prettier等开发工具
6. 建立前端基础布局和路由结构

**验收标准**：
前后端项目能够正常启动，数据库连接成功，基础API能够响应，前端页面能够正常访问，开发工具配置生效。

**相关文件**：
- `package.json` - 前端项目依赖配置
- `backend/package.json` - 后端项目依赖配置
- `backend/src/config/database.js` - 数据库配置和初始化
- `backend/src/models/schema.sql` - 数据库表结构定义
- `src/main.ts` - 前端应用入口文件
- `vite.config.ts` - Vite构建配置

---

### 任务2：学科管理完整功能
- **任务ID**: `a45f665f-d4e9-423a-a04f-a6b1f544061b`
- **优先级**: P0（最高优先级）
- **预估工期**: 2-3天
- **负责人**: Alex (工程师)
- **依赖任务**: 项目基础设施搭建

**任务描述**：
实现学科的创建、查看、删除功能，包括完整的前端界面和后端API。支持学科名称唯一性验证、长度限制、删除二次确认等业务规则。

**实现指南**：
1. 后端实现subjects表的CRUD操作API
2. 实现学科名称唯一性检查和长度验证
3. 前端实现学科列表页面，支持卡片式展示
4. 实现创建学科表单，包含实时验证
5. 实现删除确认对话框和错误处理
6. 添加加载状态和操作反馈

**验收标准**：
能够成功创建学科，学科列表正确显示，重复名称验证生效，删除功能正常工作，所有操作都有适当的用户反馈。

**相关文件**：
- `backend/src/controllers/subjectController.js` - 学科管理控制器
- `backend/src/services/subjectService.js` - 学科业务逻辑服务
- `backend/src/models/Subject.js` - 学科数据模型
- `src/pages/SubjectList.vue` - 学科列表页面
- `src/components/SubjectCard.vue` - 学科卡片组件
- `src/components/CreateSubjectModal.vue` - 创建学科弹窗
- `src/services/subjectApi.ts` - 学科API服务

---

### 任务3：访客浏览核心体验
- **任务ID**: `9aa3892e-a668-402f-be5e-a46b3a08d342`
- **优先级**: P0（最高优先级）
- **预估工期**: 3-4天
- **负责人**: Alex (工程师)
- **依赖任务**: 学科管理完整功能

**任务描述**：
实现访客首页、学科展示、文件结构浏览和基础Markdown内容渲染。支持响应式设计，适配多种设备，实现文件夹优先的排序逻辑。

**实现指南**：
1. 实现学科列表查询API和文件结构查询API
2. 前端实现访客首页，展示所有学科卡片
3. 实现文件树导航组件，支持层级展示
4. 集成Markdown渲染库（如marked.js）
5. 实现响应式布局，适配桌面、平板、手机
6. 添加面包屑导航和返回功能

**验收标准**：
访客能够浏览所有学科，文件结构正确显示，Markdown内容正确渲染，响应式布局在各种设备上正常工作，导航功能完整。

**相关文件**：
- `backend/src/controllers/fileController.js` - 文件管理控制器
- `backend/src/services/fileService.js` - 文件业务逻辑服务
- `backend/src/models/FileNode.js` - 文件节点数据模型
- `src/pages/HomePage.vue` - 访客首页
- `src/pages/SubjectDetail.vue` - 学科详情页
- `src/pages/FileViewer.vue` - 文件查看页
- `src/components/FileTree.vue` - 文件树组件
- `src/components/MarkdownRenderer.vue` - Markdown渲染组件
- `src/components/Breadcrumb.vue` - 面包屑导航组件

---

### 任务4：文件上传基础功能
- **任务ID**: `02c94bac-c46f-404d-a7b5-a3f091a2f0fe`
- **优先级**: P1（高优先级）
- **预估工期**: 3-4天
- **负责人**: Alex (工程师)
- **依赖任务**: 访客浏览核心体验

**任务描述**：
实现文件夹拖拽上传功能，支持文件类型过滤、大小限制检查、上传进度显示。实现原子化操作和事务回滚机制，确保数据一致性。

**实现指南**：
1. 后端实现文件上传API，支持multipart/form-data
2. 实现文件类型白名单过滤（.md, .jpg, .jpeg, .png, .gif, .svg, .webp）
3. 添加文件大小限制检查（单文件20MB，总量500MB）
4. 实现临时目录处理和原子化操作
5. 前端实现拖拽上传组件，支持文件夹上传
6. 添加上传进度显示和错误处理

**验收标准**：
能够成功上传文件夹，文件类型过滤生效，大小限制检查正常，上传进度正确显示，上传失败时能够正确回滚，文件结构保持完整。

**相关文件**：
- `backend/src/controllers/uploadController.js` - 文件上传控制器
- `backend/src/services/uploadService.js` - 文件上传业务逻辑
- `backend/src/middleware/fileFilter.js` - 文件过滤中间件
- `backend/src/utils/fileProcessor.js` - 文件处理工具
- `src/components/FileUploader.vue` - 文件上传组件
- `src/components/UploadProgress.vue` - 上传进度组件
- `src/services/uploadApi.ts` - 上传API服务

---

### 任务5：图片处理和Markdown增强
- **任务ID**: `adfbf1f1-671a-4bc1-bd27-c3cdd42cb9e4`
- **优先级**: P1（高优先级）
- **预估工期**: 2-3天
- **负责人**: Alex (工程师)
- **依赖任务**: 文件上传基础功能

**任务描述**：
实现图片路径处理，将Markdown中的相对路径替换为服务器API路径。增强Markdown渲染功能，支持代码高亮、表格等。实现静态资源服务接口。

**实现指南**：
1. 实现Markdown文件扫描，识别相对图片路径
2. 将相对路径替换为/api/assets/{file_node_id}格式
3. 实现/api/assets/{file_node_id}静态资源服务接口
4. 增强Markdown渲染，集成代码高亮库（如highlight.js）
5. 添加图片加载失败占位符和懒加载
6. 实现图片自动适配屏幕大小

**验收标准**：
Markdown中的图片能够正确显示，代码块有语法高亮，表格正确渲染，图片加载失败时显示占位符，图片能够自适应屏幕大小。

**相关文件**：
- `backend/src/services/imageProcessor.js` - 图片处理服务
- `backend/src/services/markdownProcessor.js` - Markdown处理服务
- `backend/src/controllers/assetController.js` - 静态资源控制器
- `backend/src/utils/pathReplacer.js` - 路径替换工具
- `src/components/EnhancedMarkdown.vue` - 增强版Markdown组件
- `src/components/ImageViewer.vue` - 图片查看组件
- `src/utils/imageLoader.ts` - 图片加载工具

---

### 任务6：管理后台和高级功能
- **任务ID**: `08774d09-c921-4ca6-aeab-d1ca16f4c48c`
- **优先级**: P1（高优先级）
- **预估工期**: 3-4天
- **负责人**: Alex (工程师)
- **依赖任务**: 图片处理和Markdown增强

**任务描述**：
实现管理员访问控制，通过特定URL路径保护管理功能。实现文件更新、覆盖功能和删除功能，包括二次确认对话框和操作日志。

**实现指南**：
1. 实现管理员访问控制中间件，检查特定URL路径
2. 创建管理后台页面，集成所有管理功能
3. 实现文件覆盖更新功能，支持同名文件夹替换
4. 实现删除功能，支持学科和单个文件删除
5. 添加二次确认对话框和操作日志记录
6. 实现批量操作和操作历史查看

**验收标准**：
管理员能够通过特定URL访问后台，文件覆盖更新正常工作，删除功能有二次确认，操作日志正确记录，批量操作功能完整。

**相关文件**：
- `backend/src/middleware/adminAuth.js` - 管理员认证中间件
- `backend/src/controllers/adminController.js` - 管理后台控制器
- `backend/src/services/adminService.js` - 管理后台业务逻辑
- `backend/src/models/OperationLog.js` - 操作日志模型
- `src/pages/AdminPanel.vue` - 管理后台主页
- `src/components/FileManager.vue` - 文件管理组件
- `src/components/ConfirmDialog.vue` - 确认对话框组件
- `src/components/OperationLog.vue` - 操作日志组件

---

### 任务7：部署和生产优化
- **任务ID**: `65000b37-587e-4989-8d94-479f9e803469`
- **优先级**: P2（中优先级）
- **预估工期**: 2-3天
- **负责人**: Alex (工程师)
- **依赖任务**: 管理后台和高级功能

**任务描述**：
实现Docker容器化部署，配置Nginx反向代理和PM2进程管理。优化性能，添加监控和日志系统，支持开发、测试、生产环境分离。

**实现指南**：
1. 编写Dockerfile和docker-compose.yml配置文件
2. 配置Nginx反向代理、静态资源缓存和HTTPS支持
3. 集成PM2进程管理，实现应用自动重启
4. 实现环境配置分离（开发、测试、生产）
5. 添加错误监控、性能指标和访问日志
6. 实现数据备份和恢复策略

**验收标准**：
应用能够通过Docker成功部署，Nginx代理正常工作，PM2进程管理生效，监控和日志系统运行正常，数据备份策略可执行。

**相关文件**：
- `Dockerfile` - Docker镜像构建文件
- `docker-compose.yml` - Docker服务编排配置
- `nginx.conf` - Nginx配置文件
- `ecosystem.config.js` - PM2进程管理配置
- `backend/src/config/environment.js` - 环境配置管理
- `scripts/backup.sh` - 数据备份脚本
- `scripts/deploy.sh` - 部署脚本
- `monitoring/docker-compose.monitoring.yml` - 监控服务配置

## 项目时间线

| 阶段 | 任务 | 时间 | 里程碑 |
|------|------|------|--------|
| Week 1 | 任务1-2 | 4-6天 | 基础设施完成，学科管理可用 |
| Week 2 | 任务3 | 3-4天 | 访客浏览体验完整 |
| Week 3 | 任务4-5 | 5-7天 | 文件上传和图片处理完成 |
| Week 4 | 任务6 | 3-4天 | 管理后台功能完整 |
| Week 5 | 任务7 | 2-3天 | 生产环境部署就绪 |

## 风险管控

| 风险 | 影响 | 缓解措施 |
|------|------|----------|
| 文件上传性能问题 | 高 | 实施分片上传，优化传输机制 |
| 图片路径处理复杂性 | 中 | 提前进行技术验证，准备备选方案 |
| SQLite并发限制 | 低 | 监控并发量，必要时升级数据库 |
| Docker部署复杂性 | 中 | 提前准备部署文档和回滚策略 |

## 质量保证

- 每个任务完成后进行代码审查
- 实施单元测试和集成测试
- 定期进行性能测试和安全检查
- 维护完整的技术文档

---

**文档结束**

*本任务规划文档将指导整个项目的开发进程，确保按时交付高质量的产品。*
