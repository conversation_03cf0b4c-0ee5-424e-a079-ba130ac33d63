# 总体架构蓝图 - 期末复习平台

## 文档信息

| 项目 | 内容 |
|------|------|
| **项目名称** | 期末复习平台 |
| **文档类型** | 总体架构蓝图 |
| **版本** | v1.0.0 |
| **创建日期** | 2025-01-08 |
| **最后更新** | 2025-01-08 |
| **负责人** | Bob (架构师) |
| **状态** | 已完成 |

## 架构概述

期末复习平台是一个基于Markdown的在线笔记管理和分享平台，采用现代化的前后端分离架构，支持管理员便捷上传管理笔记内容，访客免登录浏览所有公开内容。

### 核心设计原则

1. **简洁性**：界面简洁，功能清晰，易于使用
2. **可扩展性**：模块化设计，支持功能扩展
3. **可维护性**：代码结构清晰，文档完善
4. **性能优化**：响应时间<3秒，支持100并发用户
5. **安全性**：访问控制、数据验证、错误处理

## 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   访客用户       │   管理员用户     │      移动端用户          │
│  (浏览内容)      │  (管理内容)      │   (响应式访问)           │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                      前端层 (Vue3)                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│   访客界面       │   管理后台       │      通用组件            │
│  - 首页展示      │  - 学科管理      │   - Markdown渲染        │
│  - 文件浏览      │  - 文件上传      │   - 文件树组件          │
│  - 内容阅读      │  - 内容管理      │   - 图片查看器          │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
                            ▼ HTTP/HTTPS
┌─────────────────────────────────────────────────────────────┐
│                    Nginx 反向代理                           │
│  - 静态资源服务   - 负载均衡   - SSL终端   - 缓存策略        │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                   后端层 (Node.js + Koa)                    │
├─────────────────┬─────────────────┬─────────────────────────┤
│   控制器层       │   服务层         │      中间件层            │
│  - 路由处理      │  - 业务逻辑      │   - 认证授权            │
│  - 请求验证      │  - 数据处理      │   - 错误处理            │
│  - 响应格式      │  - 文件处理      │   - 日志记录            │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                 │
├─────────────────┬─────────────────┬─────────────────────────┤
│   SQLite数据库   │   文件存储系统   │      缓存层              │
│  - 学科数据      │  - Markdown文件  │   - 内存缓存            │
│  - 文件元数据    │  - 图片资源      │   - 静态资源缓存        │
│  - 操作日志      │  - 备份数据      │   - API响应缓存         │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 技术栈选型

### 前端技术栈

| 技术 | 版本 | 用途 | 选型理由 |
|------|------|------|----------|
| **Vue 3** | 3.4+ | 前端框架 | 组合式API、更好的TypeScript支持、性能优化 |
| **TypeScript** | 5.0+ | 类型系统 | 类型安全、代码提示、重构支持 |
| **Vite** | 5.0+ | 构建工具 | 快速热重载、现代化构建、插件生态 |
| **Ant Design Vue** | 4.0+ | UI组件库 | 企业级组件、设计规范、开箱即用 |
| **UnoCSS** | 0.58+ | CSS框架 | 原子化CSS、按需生成、性能优化 |
| **Vue Router** | 4.0+ | 路由管理 | 官方路由、类型安全、导航守卫 |
| **Pinia** | 2.0+ | 状态管理 | 轻量级、TypeScript友好、开发工具 |

### 后端技术栈

| 技术 | 版本 | 用途 | 选型理由 |
|------|------|------|----------|
| **Node.js** | 18+ | 运行时环境 | 高性能、生态丰富、JavaScript统一 |
| **Koa** | 2.14+ | Web框架 | 轻量级、中间件机制、async/await支持 |
| **better-sqlite3** | 9.0+ | 数据库驱动 | 高性能、同步API、嵌入式数据库 |
| **multer** | 1.4+ | 文件上传 | 成熟稳定、配置灵活、内存管理 |
| **marked** | 12.0+ | Markdown解析 | 轻量级、可扩展、标准兼容 |
| **highlight.js** | 11.9+ | 代码高亮 | 语言支持广、主题丰富、性能优秀 |

### 部署技术栈

| 技术 | 版本 | 用途 | 选型理由 |
|------|------|------|----------|
| **Docker** | 24.0+ | 容器化 | 环境一致性、部署简化、资源隔离 |
| **Nginx** | 1.24+ | 反向代理 | 高性能、负载均衡、静态资源服务 |
| **PM2** | 5.3+ | 进程管理 | 进程守护、负载均衡、监控日志 |

## 系统架构设计

### 分层架构

#### 1. 表现层 (Presentation Layer)

**职责**：用户界面展示、用户交互处理、数据展示格式化

**组件结构**：
```
src/
├── pages/              # 页面组件
│   ├── HomePage.vue    # 访客首页
│   ├── SubjectDetail.vue # 学科详情页
│   ├── FileViewer.vue  # 文件查看页
│   └── AdminPanel.vue  # 管理后台
├── components/         # 通用组件
│   ├── FileTree.vue    # 文件树组件
│   ├── MarkdownRenderer.vue # Markdown渲染
│   ├── FileUploader.vue # 文件上传组件
│   └── SubjectCard.vue # 学科卡片
├── services/          # API服务
│   ├── subjectApi.ts  # 学科API
│   ├── fileApi.ts     # 文件API
│   └── uploadApi.ts   # 上传API
└── utils/             # 工具函数
    ├── request.ts     # HTTP请求封装
    ├── imageLoader.ts # 图片加载工具
    └── validators.ts  # 表单验证
```

#### 2. 应用层 (Application Layer)

**职责**：业务逻辑处理、API接口提供、请求路由分发

**目录结构**：
```
backend/src/
├── controllers/       # 控制器层
│   ├── subjectController.js # 学科管理
│   ├── fileController.js    # 文件管理
│   ├── uploadController.js  # 文件上传
│   ├── assetController.js   # 静态资源
│   └── adminController.js   # 管理后台
├── services/          # 服务层
│   ├── subjectService.js    # 学科业务逻辑
│   ├── fileService.js       # 文件业务逻辑
│   ├── uploadService.js     # 上传业务逻辑
│   ├── markdownProcessor.js # Markdown处理
│   └── imageProcessor.js    # 图片处理
├── middleware/        # 中间件
│   ├── adminAuth.js   # 管理员认证
│   ├── fileFilter.js  # 文件过滤
│   ├── errorHandler.js # 错误处理
│   └── logger.js      # 日志记录
└── utils/             # 工具函数
    ├── fileProcessor.js # 文件处理工具
    ├── pathReplacer.js  # 路径替换
    └── validator.js     # 数据验证
```

#### 3. 数据层 (Data Layer)

**职责**：数据持久化、数据访问、文件存储管理

**数据模型设计**：
```
backend/src/
├── models/            # 数据模型
│   ├── Subject.js     # 学科模型
│   ├── FileNode.js    # 文件节点模型
│   └── OperationLog.js # 操作日志模型
├── config/            # 配置文件
│   ├── database.js    # 数据库配置
│   └── storage.js     # 存储配置
└── migrations/        # 数据库迁移
    └── schema.sql     # 数据库表结构
```

## 数据库设计

### 核心表结构

#### 学科表 (subjects)
```sql
CREATE TABLE subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_subjects_name ON subjects(name);
CREATE INDEX idx_subjects_created_at ON subjects(created_at);
```

#### 文件节点表 (file_nodes)
```sql
CREATE TABLE file_nodes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_id INTEGER NOT NULL,
    parent_id INTEGER NULL,
    name VARCHAR(255) NOT NULL,
    type ENUM('file', 'folder') NOT NULL,
    relative_path TEXT NOT NULL,
    storage_url TEXT NULL,
    file_size INTEGER DEFAULT 0,
    mime_type VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_file_nodes_subject_id ON file_nodes(subject_id);
CREATE INDEX idx_file_nodes_parent_id ON file_nodes(parent_id);
CREATE INDEX idx_file_nodes_type ON file_nodes(type);
CREATE INDEX idx_file_nodes_path ON file_nodes(relative_path);
```

#### 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id INTEGER,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_operation_logs_type ON operation_logs(operation_type);
CREATE INDEX idx_operation_logs_created_at ON operation_logs(created_at);
```

### 数据关系图

```
subjects (1) ──────── (N) file_nodes
    │                      │
    │                      │ (parent_id)
    │                      │
    └── (subject_id) ──────┴── (self-reference)

operation_logs ──── (记录所有操作)
```

## API接口设计

### RESTful API规范

#### 基础配置
- **Base URL**: `/api/v1`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

#### 统一响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 错误响应格式
```json
{
  "success": false,
  "code": 400,
  "message": "请求参数错误",
  "error": "详细错误信息",
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

### 核心API接口

#### 学科管理API
```
GET    /api/v1/subjects           # 获取所有学科列表
POST   /api/v1/subjects           # 创建新学科 (管理员)
GET    /api/v1/subjects/:id       # 获取学科详情
PUT    /api/v1/subjects/:id       # 更新学科信息 (管理员)
DELETE /api/v1/subjects/:id       # 删除学科 (管理员)
```

#### 文件管理API
```
GET    /api/v1/subjects/:id/files # 获取学科文件结构
GET    /api/v1/files/:id          # 获取文件内容
POST   /api/v1/subjects/:id/upload # 上传文件到学科 (管理员)
PUT    /api/v1/files/:id          # 更新文件内容 (管理员)
DELETE /api/v1/files/:id          # 删除文件 (管理员)
```

#### 静态资源API
```
GET    /api/v1/assets/:fileNodeId # 获取静态资源文件
```

#### 管理后台API
```
GET    /api/v1/admin/dashboard    # 管理后台仪表盘 (管理员)
GET    /api/v1/admin/logs         # 获取操作日志 (管理员)
POST   /api/v1/admin/batch-delete # 批量删除操作 (管理员)
```

## 安全架构设计

### 访问控制策略

#### 1. 路径级别访问控制
```javascript
// 管理员路径保护
const ADMIN_PATHS = [
  '/admin',
  '/api/v1/subjects (POST/PUT/DELETE)',
  '/api/v1/files (POST/PUT/DELETE)',
  '/api/v1/admin/*'
];

// 访客开放路径
const PUBLIC_PATHS = [
  '/',
  '/subjects/*',
  '/files/*',
  '/api/v1/subjects (GET)',
  '/api/v1/files (GET)',
  '/api/v1/assets/*'
];
```

#### 2. 文件安全策略
```javascript
// 文件类型白名单
const ALLOWED_FILE_TYPES = [
  '.md', '.markdown',           // Markdown文件
  '.jpg', '.jpeg', '.png',      // 图片文件
  '.gif', '.svg', '.webp'       // 其他图片格式
];

// 文件大小限制
const FILE_SIZE_LIMITS = {
  singleFile: 20 * 1024 * 1024,    // 单文件20MB
  totalUpload: 500 * 1024 * 1024   // 单次上传500MB
};
```

#### 3. 数据验证策略
```javascript
// 输入验证规则
const VALIDATION_RULES = {
  subjectName: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/
  },
  fileName: {
    required: true,
    maxLength: 255,
    pattern: /^[^<>:"/\\|?*]+$/
  }
};
```

### 错误处理策略

#### 1. 错误分类
```javascript
const ERROR_CODES = {
  // 客户端错误 (4xx)
  VALIDATION_ERROR: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  FILE_TOO_LARGE: 413,
  UNSUPPORTED_MEDIA_TYPE: 415,
  
  // 服务器错误 (5xx)
  INTERNAL_SERVER_ERROR: 500,
  DATABASE_ERROR: 501,
  FILE_SYSTEM_ERROR: 502
};
```

#### 2. 错误处理中间件
```javascript
// 全局错误处理
async function errorHandler(ctx, next) {
  try {
    await next();
  } catch (err) {
    ctx.status = err.status || 500;
    ctx.body = {
      success: false,
      code: ctx.status,
      message: err.message || '服务器内部错误',
      timestamp: new Date().toISOString()
    };
    
    // 记录错误日志
    logger.error('API Error:', err);
  }
}
```

## 性能优化策略

### 前端性能优化

#### 1. 代码分割与懒加载
```javascript
// 路由级别代码分割
const routes = [
  {
    path: '/',
    component: () => import('@/pages/HomePage.vue')
  },
  {
    path: '/admin',
    component: () => import('@/pages/AdminPanel.vue')
  }
];

// 组件级别懒加载
const MarkdownRenderer = defineAsyncComponent(
  () => import('@/components/MarkdownRenderer.vue')
);
```

#### 2. 图片优化策略
```javascript
// 图片懒加载
const useImageLazyLoad = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        observer.unobserve(img);
      }
    });
  });
  
  return { observer };
};

// 图片压缩和格式优化
const optimizeImage = (file) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // 压缩逻辑
      canvas.toBlob(resolve, 'image/webp', 0.8);
    };
    
    img.src = URL.createObjectURL(file);
  });
};
```

### 后端性能优化

#### 1. 数据库优化
```sql
-- 查询优化索引
CREATE INDEX idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id);
CREATE INDEX idx_file_nodes_type_name ON file_nodes(type, name);

-- 分页查询优化
SELECT * FROM file_nodes 
WHERE subject_id = ? 
ORDER BY type DESC, name ASC 
LIMIT ? OFFSET ?;
```

#### 2. 缓存策略
```javascript
// 内存缓存
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 }); // 10分钟缓存

// API响应缓存
const cacheMiddleware = (ttl = 300) => {
  return async (ctx, next) => {
    const key = `api:${ctx.method}:${ctx.path}:${JSON.stringify(ctx.query)}`;
    const cached = cache.get(key);
    
    if (cached) {
      ctx.body = cached;
      return;
    }
    
    await next();
    
    if (ctx.status === 200) {
      cache.set(key, ctx.body, ttl);
    }
  };
};
```

#### 3. 文件处理优化
```javascript
// 文件上传流处理
const processFileStream = (stream, options) => {
  return new Promise((resolve, reject) => {
    const chunks = [];
    
    stream.on('data', chunk => {
      chunks.push(chunk);
      
      // 检查文件大小限制
      const currentSize = chunks.reduce((size, chunk) => size + chunk.length, 0);
      if (currentSize > options.maxSize) {
        reject(new Error('文件大小超出限制'));
      }
    });
    
    stream.on('end', () => {
      resolve(Buffer.concat(chunks));
    });
    
    stream.on('error', reject);
  });
};
```

## 部署架构设计

### Docker容器化部署

#### 1. 多阶段构建Dockerfile
```dockerfile
# 前端构建阶段
FROM node:18-alpine AS frontend-builder
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ ./
RUN npm run build

# 后端构建阶段
FROM node:18-alpine AS backend-builder
WORKDIR /app/backend
COPY backend/package*.json ./
RUN npm ci --only=production
COPY backend/ ./

# 生产环境镜像
FROM node:18-alpine AS production
WORKDIR /app

# 安装PM2
RUN npm install -g pm2

# 复制后端代码
COPY --from=backend-builder /app/backend ./backend
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# 创建数据目录
RUN mkdir -p /app/data /app/uploads

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["pm2-runtime", "start", "backend/ecosystem.config.js"]
```

#### 2. Docker Compose配置
```yaml
version: '3.8'

services:
  app:
    build: .
    container_name: term-review-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/database.sqlite
      - UPLOAD_PATH=/app/uploads
    networks:
      - term-review-network

  nginx:
    image: nginx:alpine
    container_name: term-review-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - app
    networks:
      - term-review-network

networks:
  term-review-network:
    driver: bridge

volumes:
  data:
  uploads:
  logs:
```

#### 3. Nginx配置
```nginx
upstream backend {
    server app:3000;
}

server {
    listen 80;
    server_name localhost;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name localhost;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态资源
    location /uploads/ {
        alias /var/www/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 前端应用
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### PM2进程管理配置

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'term-review-api',
    script: './src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

## 监控与日志

### 应用监控

#### 1. 性能指标监控
```javascript
// 性能监控中间件
const performanceMonitor = async (ctx, next) => {
  const start = Date.now();
  
  await next();
  
  const duration = Date.now() - start;
  
  // 记录性能指标
  logger.info('API Performance', {
    method: ctx.method,
    path: ctx.path,
    status: ctx.status,
    duration: `${duration}ms`,
    timestamp: new Date().toISOString()
  });
  
  // 慢查询告警
  if (duration > 3000) {
    logger.warn('Slow API detected', {
      method: ctx.method,
      path: ctx.path,
      duration: `${duration}ms`
    });
  }
};
```

#### 2. 错误监控
```javascript
// 错误监控和告警
const errorMonitor = (error, ctx) => {
  logger.error('Application Error', {
    error: error.message,
    stack: error.stack,
    method: ctx.method,
    path: ctx.path,
    userAgent: ctx.headers['user-agent'],
    ip: ctx.ip,
    timestamp: new Date().toISOString()
  });
  
  // 关键错误告警
  if (error.status >= 500) {
    // 发送告警通知
    sendAlert({
      type: 'critical',
      message: `服务器错误: ${error.message}`,
      path: ctx.path
    });
  }
};
```

### 日志管理

#### 1. 结构化日志
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'term-review-api' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

#### 2. 业务日志
```javascript
// 操作日志记录
const logOperation = async (operation, target, details) => {
  await OperationLog.create({
    operation_type: operation,
    target_type: target.type,
    target_id: target.id,
    details: JSON.stringify(details),
    ip_address: ctx.ip,
    user_agent: ctx.headers['user-agent']
  });
};

// 使用示例
await logOperation('CREATE_SUBJECT', { type: 'subject', id: subjectId }, {
  name: subjectName,
  description: subjectDescription
});
```

## 扩展性设计

### 模块化架构

#### 1. 插件化设计
```javascript
// 插件接口定义
class Plugin {
  constructor(options) {
    this.options = options;
  }
  
  async install(app) {
    // 插件安装逻辑
  }
  
  async uninstall(app) {
    // 插件卸载逻辑
  }
}

// 插件管理器
class PluginManager {
  constructor() {
    this.plugins = new Map();
  }
  
  async loadPlugin(name, plugin) {
    this.plugins.set(name, plugin);
    await plugin.install(this.app);
  }
  
  async unloadPlugin(name) {
    const plugin = this.plugins.get(name);
    if (plugin) {
      await plugin.uninstall(this.app);
      this.plugins.delete(name);
    }
  }
}
```

#### 2. 微服务预留接口
```javascript
// 服务接口抽象
class ServiceInterface {
  async getSubjects() {
    throw new Error('Method not implemented');
  }
  
  async createSubject(data) {
    throw new Error('Method not implemented');
  }
}

// 本地服务实现
class LocalSubjectService extends ServiceInterface {
  async getSubjects() {
    return await Subject.findAll();
  }
  
  async createSubject(data) {
    return await Subject.create(data);
  }
}

// 远程服务实现（预留）
class RemoteSubjectService extends ServiceInterface {
  constructor(apiUrl) {
    super();
    this.apiUrl = apiUrl;
  }
  
  async getSubjects() {
    const response = await fetch(`${this.apiUrl}/subjects`);
    return await response.json();
  }
}
```

### 数据库迁移策略

#### 1. 版本化迁移
```javascript
// 迁移脚本管理
class MigrationManager {
  constructor(db) {
    this.db = db;
  }
  
  async getCurrentVersion() {
    try {
      const result = await this.db.get('SELECT version FROM schema_version ORDER BY id DESC LIMIT 1');
      return result ? result.version : 0;
    } catch (error) {
      return 0;
    }
  }
  
  async migrate() {
    const currentVersion = await this.getCurrentVersion();
    const migrations = this.getMigrations();
    
    for (const migration of migrations) {
      if (migration.version > currentVersion) {
        await this.runMigration(migration);
      }
    }
  }
  
  getMigrations() {
    return [
      {
        version: 1,
        name: 'create_initial_tables',
        up: `
          CREATE TABLE subjects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) UNIQUE NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          );
          
          CREATE TABLE file_nodes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subject_id INTEGER NOT NULL,
            parent_id INTEGER NULL,
            name VARCHAR(255) NOT NULL,
            type TEXT CHECK(type IN ('file', 'folder')) NOT NULL,
            relative_path TEXT NOT NULL,
            storage_url TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (subject_id) REFERENCES subjects(id),
            FOREIGN KEY (parent_id) REFERENCES file_nodes(id)
          );
        `,
        down: `
          DROP TABLE file_nodes;
          DROP TABLE subjects;
        `
      }
    ];
  }
}
```

## 开发优先级与实施计划

### 基于垂直切片的开发顺序

#### Phase 1: 核心基础 (P0 - Week 1)
1. **项目基础设施搭建**
   - 前后端项目初始化
   - 数据库设计和初始化
   - 基础API框架搭建
   - 开发环境配置

2. **学科管理完整功能**
   - 学科CRUD API开发
   - 学科管理界面开发
   - 数据验证和错误处理

#### Phase 2: 用户体验 (P0 - Week 2)
3. **访客浏览核心体验**
   - 首页和学科展示
   - 文件结构浏览
   - Markdown渲染功能
   - 响应式布局实现

#### Phase 3: 内容管理 (P1 - Week 3)
4. **文件上传基础功能**
   - 文件上传API开发
   - 拖拽上传界面
   - 进度显示和错误处理
   - 原子化操作实现

5. **图片处理和Markdown增强**
   - 图片路径处理
   - 静态资源服务
   - 代码高亮和表格支持
   - 图片优化和懒加载

#### Phase 4: 管理功能 (P1 - Week 4)
6. **管理后台和高级功能**
   - 管理员访问控制
   - 文件管理界面
   - 操作日志记录
   - 批量操作功能

#### Phase 5: 生产部署 (P2 - Week 5)
7. **部署和生产优化**
   - Docker容器化
   - Nginx配置
   - 监控和日志系统
   - 性能优化和安全加固

### 架构演进路线图

#### 短期目标 (1-3个月)
- 完成MVP功能开发
- 基础性能优化
- 安全加固
- 用户反馈收集

#### 中期目标 (3-6个月)
- 功能扩展（搜索、标签、评论）
- 性能深度优化
- 移动端适配优化
- 数据分析功能

#### 长期目标 (6-12个月)
- 微服务架构迁移
- 多租户支持
- 高可用部署
- 国际化支持

## 风险评估与缓解策略

### 技术风险

#### 1. 文件上传性能风险
**风险描述**：大文件上传可能导致服务器内存溢出或响应超时

**缓解策略**：
- 实施分片上传机制
- 使用流式处理避免内存积累
- 设置合理的文件大小限制
- 实现上传进度监控和断点续传

#### 2. SQLite并发限制风险
**风险描述**：SQLite在高并发场景下可能出现锁竞争

**缓解策略**：
- 优化数据库查询和索引
- 实施连接池管理
- 监控并发量和响应时间
- 预留PostgreSQL迁移方案

#### 3. 图片路径处理复杂性风险
**风险描述**：Markdown中的相对路径处理可能出现边缘情况

**缓解策略**：
- 详细的路径解析测试
- 多种路径格式的兼容性处理
- 错误情况的降级处理
- 完整的单元测试覆盖

### 业务风险

#### 1. 数据安全风险
**风险描述**：用户上传的敏感内容可能泄露

**缓解策略**：
- 严格的文件类型验证
- 访问权限控制
- 定期安全审计
- 数据备份和恢复机制

#### 2. 存储空间风险
**风险描述**：大量文件上传可能导致存储空间不足

**缓解策略**：
- 存储空间监控和告警
- 文件清理和归档策略
- 存储扩展方案
- 文件压缩和去重

## 总结

本架构蓝图为期末复习平台提供了完整的技术架构设计，具备以下特点：

### 架构优势

1. **模块化设计**：清晰的分层架构，便于开发和维护
2. **可扩展性**：支持功能扩展和技术栈升级
3. **高性能**：多层缓存和优化策略
4. **安全可靠**：完善的安全控制和错误处理
5. **易于部署**：容器化部署，环境一致性

### 技术选型合理性

1. **Vue3 + TypeScript**：现代化前端技术栈，开发效率高
2. **Node.js + Koa**：轻量级后端框架，性能优秀
3. **SQLite**：嵌入式数据库，部署简单，适合中小规模应用
4. **Docker**：容器化部署，环境一致性和可移植性

### 开发友好性

1. **垂直切片支持**：架构设计完全支持垂直切片开发方式
2. **清晰的模块边界**：便于团队协作开发
3. **完善的文档**：详细的架构说明和实施指南
4. **渐进式实施**：支持分阶段开发和部署

本架构蓝图将为项目的成功实施提供坚实的技术基础，确保系统的可靠性、可维护性和可扩展性。

---

**文档结束**

*本架构蓝图将指导整个项目的技术实施，确保系统架构的合理性和可扩展性。*